'use client';
import React, { useState } from 'react';
import { motion } from "framer-motion";
import { useParams } from 'next/navigation';
import { type Locale } from '@/lib/i18n';
import { getAllBlogPosts, type BlogLocale } from '@/data/blog-posts';
import Hero from '@/components/ui/Hero';
import Card, { CardContent, CardHeader } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { NavigationIcons } from '@/components/ui/Icons';
import Link from 'next/link';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// 现代化博客文章卡片组件
const ModernBlogCard = ({
  slug,
  title,
  excerpt,
  date,
  tags,
  category,
  readTime,
  author,
  featured = false,
  locale
}: {
  slug: string;
  title: string;
  excerpt: string;
  date: string;
  tags: string[];
  category: string;
  readTime: string;
  author: string;
  featured?: boolean;
  locale: Locale;
}) => (
  <motion.div
    variants={fadeInUp}
    className="group"
  >
    <Link href={`/${locale}/blog/${slug}`} prefetch={false}>
      <Card
        className={`h-full transition-all duration-300 hover:scale-105 hover:shadow-xl border border-neutral-200 hover:border-primary-200 cursor-pointer ${
          featured ? 'ring-2 ring-primary-200' : ''
        }`}
        padding="none"
        shadow="lg"
        border={false}
      >
      {/* 文章头部 */}
      <CardHeader className="p-6">
        <div className="flex items-start justify-between mb-3">
          <Badge variant={featured ? "primary" : "outline"} size="sm">
            {category}
          </Badge>
          {featured && (
            <Badge variant="secondary" size="sm">
              {locale === 'zh' ? '精选' : 'Featured'}
            </Badge>
          )}
        </div>
        <h3 className="text-xl font-semibold text-neutral-900 mb-3 group-hover:text-primary-600 transition-colors line-clamp-2">
          {title}
        </h3>
        <p className="text-neutral-600 text-sm leading-relaxed line-clamp-3">
          {excerpt}
        </p>
      </CardHeader>

      <CardContent className="p-6 pt-0">
        {/* 标签 */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            {tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" size="sm">
                {tag}
              </Badge>
            ))}
            {tags.length > 3 && (
              <Badge variant="outline" size="sm">
                +{tags.length - 3}
              </Badge>
            )}
          </div>
        </div>

        {/* 文章信息 */}
        <div className="flex items-center justify-between text-sm text-neutral-500 mb-4">
          <div className="flex items-center space-x-4">
            <span>{author}</span>
            <span>•</span>
            <span>{readTime}</span>
          </div>
          <span>{new Date(date).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}</span>
        </div>

        {/* 阅读按钮 */}
        <Button
          variant="outline"
          size="sm"
          fullWidth
          className="group-hover:border-primary-600 group-hover:text-primary-600"
          icon={NavigationIcons.Arrow}
          iconPosition="right"
        >
          {locale === 'zh' ? '阅读全文' : 'Read More'}
        </Button>
      </CardContent>
    </Card>
    </Link>
  </motion.div>
);

export default function Blog() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;
  const isZh = localeTyped === 'zh';

  // 确保 locale 是博客支持的语言，如果不支持则默认为英文
  const blogLocale: BlogLocale = (localeTyped === 'zh' || localeTyped === 'en') ? localeTyped : 'en';

  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // 文章分类
  const categories = [
    { id: 'all', name: isZh ? '全部' : 'All' },
    { id: 'ios', name: isZh ? 'iOS 开发' : 'iOS Development' },
    { id: 'frontend', name: isZh ? '前端开发' : 'Frontend' },
    { id: 'backend', name: isZh ? '后端开发' : 'Backend' },
    { id: 'design', name: isZh ? '设计' : 'Design' },
    { id: 'devops', name: isZh ? '运维' : 'DevOps' },
    { id: 'tutorial', name: isZh ? '教程' : 'Tutorial' }
  ];

  // 博客文章数据 - 使用新的数据结构
  const posts = getAllBlogPosts(blogLocale);

  // 根据选中的分类筛选文章
  const filteredPosts = selectedCategory === 'all'
    ? posts
    : posts.filter(post => post.category === selectedCategory);

  // 分离精选文章和普通文章
  const featuredPosts = filteredPosts.filter(post => post.featured);
  const regularPosts = filteredPosts.filter(post => !post.featured);

  return (
    <>
      {/* Hero 部分 */}
      <Hero
        title={isZh ? '技术博客' : 'Tech Blog'}
        subtitle={isZh
          ? '分享我们的技术实践、设计思考和行业洞察，与开发者社区一起成长'
          : 'Sharing our technical practices, design insights, and industry perspectives to grow together with the developer community'}
        align="center"
      />

      {/* 分类筛选 */}
      <section className="py-12 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-8"
          >
            <h2 className="text-2xl font-semibold text-neutral-900 mb-4">
              {isZh ? '按分类浏览' : 'Browse by Category'}
            </h2>
            <div className="flex flex-wrap justify-center gap-3">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "primary" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className="transition-all duration-200"
                >
                  {category.name}
                </Button>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* 精选文章 */}
      {featuredPosts.length > 0 && (
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={fadeInUp}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-semibold text-neutral-900 mb-4">
                {isZh ? '精选文章' : 'Featured Articles'}
              </h2>
              <p className="text-lg text-neutral-600">
                {isZh ? '我们精心挑选的优质技术内容' : 'Carefully curated quality technical content'}
              </p>
            </motion.div>

            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={staggerContainer}
              className="grid md:grid-cols-2 gap-8 mb-16"
            >
              {featuredPosts.map((post) => (
                <ModernBlogCard
                  key={post.title}
                  {...post}
                  locale={blogLocale}
                />
              ))}
            </motion.div>
          </div>
        </section>
      )}

      {/* 所有文章 */}
      <section className={`py-16 ${featuredPosts.length > 0 ? 'bg-neutral-50' : 'bg-white'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-semibold text-neutral-900 mb-4">
              {isZh ? '最新文章' : 'Latest Articles'}
            </h2>
            <p className="text-lg text-neutral-600">
              {isZh ? '探索更多技术话题和实践经验' : 'Explore more technical topics and practical experiences'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {regularPosts.map((post) => (
              <ModernBlogCard
                key={post.title}
                {...post}
                locale={blogLocale}
              />
            ))}
          </motion.div>

          {filteredPosts.length === 0 && (
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={fadeInUp}
              className="text-center py-16"
            >
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-xl font-medium text-neutral-900 mb-2">
                {isZh ? '暂无相关文章' : 'No Articles Found'}
              </h3>
              <p className="text-neutral-600">
                {isZh ? '该分类的文章正在创作中，敬请期待' : 'Articles for this category are being created, stay tuned'}
              </p>
            </motion.div>
          )}
        </div>
      </section>

      {/* CTA 部分 */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl font-semibold text-white mb-6">
              {isZh ? '想要了解更多技术内容？' : 'Want to Learn More Technical Content?'}
            </h2>
            <p className="text-xl text-primary-100 mb-8">
              {isZh
                ? '关注我们的博客，获取最新的技术文章和行业洞察'
                : 'Follow our blog for the latest technical articles and industry insights'}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="secondary"
                size="lg"
                onClick={() => window.location.href = `/${locale}/contact`}
                icon={NavigationIcons.Arrow}
                iconPosition="right"
              >
                {isZh ? '联系我们' : 'Contact Us'}
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => window.location.href = `/${locale}/services`}
                className="border-white text-white hover:bg-white hover:text-primary-600"
              >
                {isZh ? '了解服务' : 'Our Services'}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
}


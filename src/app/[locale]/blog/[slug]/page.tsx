'use client';

import { useParams } from 'next/navigation';
import { notFound } from 'next/navigation';
import { motion } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import { getBlogPost, getRelatedPosts, type BlogPost, type BlogLocale } from '@/data/blog-posts';
import { Locale } from '@/lib/i18n';
import Button from '@/components/ui/Button';
import Card, { CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Badge from '@/components/ui/Badge';
import { NavigationIcons } from '@/components/ui/Icons';
import Link from 'next/link';

// 引入代码高亮样式
import 'highlight.js/styles/github.css';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

// 文章内容组件
const ArticleContent = ({ content }: { content: string }) => {
  return (
    <div className="prose prose-lg max-w-none prose-headings:text-neutral-900 prose-p:text-neutral-700 prose-a:text-primary-600 prose-strong:text-neutral-900 prose-code:text-primary-600 prose-code:bg-primary-50 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-pre:bg-neutral-900 prose-pre:text-neutral-100 prose-blockquote:border-primary-200 prose-blockquote:text-neutral-600">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight, rehypeRaw]}
        components={{
          // 自定义代码块样式
          pre: ({ children, ...props }) => (
            <pre {...props} className="bg-neutral-900 text-neutral-100 p-4 rounded-lg overflow-x-auto">
              {children}
            </pre>
          ),
          // 自定义行内代码样式
          code: ({ children, className, ...props }) => {
            const isInline = !className;
            return isInline ? (
              <code {...props} className="text-primary-600 bg-primary-50 px-1 py-0.5 rounded text-sm">
                {children}
              </code>
            ) : (
              <code {...props} className={className}>
                {children}
              </code>
            );
          },
          // 自定义链接样式
          a: ({ children, href, ...props }) => (
            <a
              {...props}
              href={href}
              className="text-primary-600 hover:text-primary-700 underline"
              target={href?.startsWith('http') ? '_blank' : undefined}
              rel={href?.startsWith('http') ? 'noopener noreferrer' : undefined}
            >
              {children}
            </a>
          ),
          // 自定义表格样式
          table: ({ children, ...props }) => (
            <div className="overflow-x-auto">
              <table {...props} className="min-w-full divide-y divide-neutral-200">
                {children}
              </table>
            </div>
          ),
          th: ({ children, ...props }) => (
            <th {...props} className="px-6 py-3 bg-neutral-50 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
              {children}
            </th>
          ),
          td: ({ children, ...props }) => (
            <td {...props} className="px-6 py-4 whitespace-nowrap text-sm text-neutral-900">
              {children}
            </td>
          )
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

// 相关文章卡片组件
const RelatedPostCard = ({ post, locale }: { post: BlogPost; locale: BlogLocale }) => (
  <motion.div variants={fadeInUp}>
    <Link href={`/${locale}/blog/${post.slug}`}>
      <Card 
        className="h-full transition-all duration-300 hover:scale-105 hover:shadow-xl border border-neutral-200 hover:border-primary-200"
        padding="lg"
        shadow="lg"
        border={false}
        hover
      >
        <CardHeader>
          <div className="flex items-center justify-between mb-2">
            <Badge variant="outline" size="sm">
              {post.category}
            </Badge>
            <span className="text-sm text-neutral-500">
              {new Date(post.date).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}
            </span>
          </div>
          <CardTitle size="md" className="line-clamp-2">
            {post.title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-neutral-600 text-sm line-clamp-3 mb-4">
            {post.excerpt}
          </p>
          <div className="flex items-center justify-between text-sm text-neutral-500">
            <span>{post.author}</span>
            <span>{post.readTime}</span>
          </div>
        </CardContent>
      </Card>
    </Link>
  </motion.div>
);

export default function BlogPost() {
  const params = useParams();
  const locale = params.locale as Locale;
  const slug = params.slug as string;
  const isZh = locale === 'zh';

  // 确保 locale 是博客支持的语言，如果不支持则默认为英文
  const blogLocale: BlogLocale = (locale === 'zh' || locale === 'en') ? locale : 'en';

  // 获取文章数据
  const post = getBlogPost(slug, blogLocale);
  
  if (!post) {
    notFound();
  }

  // 获取相关文章
  const relatedPosts = getRelatedPosts(slug, blogLocale, 3);

  return (
    <div className="min-h-screen bg-white">
      {/* 文章头部 */}
      <section className="py-16 bg-gradient-to-br from-primary-50 to-primary-100">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeInUp}
            className="text-center"
          >
            {/* 返回按钮 */}
            <div className="mb-8">
              <Link href={`/${locale}/blog`}>
                <Button
                  variant="outline"
                  size="sm"
                  icon={NavigationIcons.Arrow}
                  iconPosition="left"
                  className="rotate-180"
                >
                  {isZh ? '返回博客' : 'Back to Blog'}
                </Button>
              </Link>
            </div>

            {/* 文章分类 */}
            <div className="mb-4">
              <Badge variant="primary" size="md">
                {post.category}
              </Badge>
            </div>

            {/* 文章标题 */}
            <h1 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              {post.title}
            </h1>

            {/* 文章摘要 */}
            <p className="text-xl text-neutral-600 mb-8 max-w-3xl mx-auto">
              {post.excerpt}
            </p>

            {/* 文章元信息 */}
            <div className="flex flex-wrap items-center justify-center gap-6 text-neutral-500">
              <div className="flex items-center space-x-2">
                <span className="font-medium">{post.author}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>{new Date(post.date).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>{post.readTime}</span>
              </div>
            </div>

            {/* 标签 */}
            <div className="flex flex-wrap justify-center gap-2 mt-6">
              {post.tags.map((tag) => (
                <Badge key={tag} variant="outline" size="sm">
                  {tag}
                </Badge>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* 文章内容 */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeInUp}
            transition={{ delay: 0.2 }}
          >
            <ArticleContent content={post.content} />
          </motion.div>
        </div>
      </section>

      {/* 相关文章 */}
      {relatedPosts.length > 0 && (
        <section className="py-16 bg-neutral-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={fadeInUp}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-semibold text-neutral-900 mb-4">
                {isZh ? '相关文章' : 'Related Articles'}
              </h2>
              <p className="text-lg text-neutral-600">
                {isZh ? '您可能还感兴趣的其他文章' : 'Other articles you might be interested in'}
              </p>
            </motion.div>

            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={staggerContainer}
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {relatedPosts.map((relatedPost) => (
                <RelatedPostCard
                  key={relatedPost.slug}
                  post={relatedPost}
                  locale={blogLocale}
                />
              ))}
            </motion.div>
          </div>
        </section>
      )}

      {/* CTA 部分 */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl font-semibold text-white mb-6">
              {isZh ? '喜欢这篇文章？' : 'Enjoyed This Article?'}
            </h2>
            <p className="text-xl text-primary-100 mb-8">
              {isZh
                ? '关注我们的博客，获取更多技术文章和行业洞察'
                : 'Follow our blog for more technical articles and industry insights'}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href={`/${locale}/blog`}>
                <Button
                  variant="secondary"
                  size="lg"
                  icon={NavigationIcons.Arrow}
                  iconPosition="right"
                >
                  {isZh ? '查看更多文章' : 'View More Articles'}
                </Button>
              </Link>
              <Link href={`/${locale}/contact`}>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-white text-white hover:bg-white hover:text-primary-600"
                >
                  {isZh ? '联系我们' : 'Contact Us'}
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}

'use client';

import Link from "next/link";
import { motion } from "framer-motion";
import { useParams } from "next/navigation";
import { t, type Locale } from '@/lib/i18n';
import {
  AppDevIcon,
  MiniProgramIcon,
  BackendIcon,
  WebDevIcon,
  NavigationIcons,
  ContactIcons
} from '@/components/ui/Icons';
import Card, { CardTitle, CardDescription, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Container from '@/components/ui/Container';
import Stats from '@/components/ui/Stats';
import Testimonial from '@/components/ui/Testimonial';
import Badge from '@/components/ui/Badge';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};



// 特色亮点卡片组件
const FeatureCard = ({ icon, title, description }: { icon: string; title: string; description: string }) => (
  <motion.div
    variants={fadeInUp}
    className="text-center group"
  >
    <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
      {icon}
    </div>
    <h3 className="text-lg font-semibold text-neutral-900 mb-2 group-hover:text-primary-600 transition-colors">
      {title}
    </h3>
    <p className="text-neutral-600 text-sm leading-relaxed">
      {description}
    </p>
  </motion.div>
);

// 客户案例卡片组件
const CaseStudyCard = ({ title, description, result, industry }: {
  title: string;
  description: string;
  result: string;
  industry: string;
}) => (
  <motion.div
    variants={fadeInUp}
    className="group"
  >
    <Card
      className="h-full transition-all duration-300 hover:scale-105 hover:shadow-xl border border-neutral-200 hover:border-primary-200"
      padding="lg"
      shadow="lg"
      border={false}
    >
      <CardContent className="p-6">
        <div className="mb-4">
          <Badge color="neutral" className="mb-3">
            {industry}
          </Badge>
          <h3 className="text-lg font-semibold text-neutral-900 mb-2 group-hover:text-primary-600 transition-colors">
            {title}
          </h3>
          <p className="text-neutral-600 text-sm leading-relaxed mb-4">
            {description}
          </p>
        </div>
        <div className="pt-4 border-t border-neutral-100">
          <div className="text-sm font-medium text-primary-600">
            {result}
          </div>
        </div>
      </CardContent>
    </Card>
  </motion.div>
);

export default function Home() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;
  const isZh = localeTyped === 'zh';

  // 现代化服务数据
  const services = [
    {
      title: isZh ? '移动应用开发' : 'Mobile App Development',
      description: isZh
        ? '原生 iOS/Android 应用和跨平台解决方案，提供卓越的用户体验和性能表现。'
        : 'Native iOS/Android apps and cross-platform solutions, delivering exceptional user experience and performance.',
      IconComponent: AppDevIcon,
      features: isZh
        ? ['原生 iOS/Android 开发', 'React Native 跨平台', 'Flutter 应用开发', 'App Store 上架指导']
        : ['Native iOS/Android Development', 'React Native Cross-platform', 'Flutter App Development', 'App Store Launch Guidance'],
      isPopular: true
    },
    {
      title: isZh ? '小程序开发' : 'Mini-Program Development',
      description: isZh
        ? '微信、支付宝、抖音等平台小程序开发，快速触达用户，降低获客成本。'
        : 'WeChat, Alipay, TikTok and other platform mini-programs, quickly reach users and reduce customer acquisition costs.',
      IconComponent: MiniProgramIcon,
      features: isZh
        ? ['微信小程序开发', '支付宝小程序', '抖音小程序', '多平台适配']
        : ['WeChat Mini-Program', 'Alipay Mini-Program', 'TikTok Mini-Program', 'Multi-platform Adaptation']
    },
    {
      title: isZh ? '后端系统开发' : 'Backend System Development',
      description: isZh
        ? '可扩展的后端架构设计，支持高并发访问，确保系统稳定性和安全性。'
        : 'Scalable backend architecture design, supporting high concurrent access, ensuring system stability and security.',
      IconComponent: BackendIcon,
      features: isZh
        ? ['RESTful API 设计', '微服务架构', '数据库优化', '云服务部署']
        : ['RESTful API Design', 'Microservices Architecture', 'Database Optimization', 'Cloud Service Deployment']
    },
    {
      title: isZh ? '网站开发' : 'Website Development',
      description: isZh
        ? '响应式网站设计，SEO 优化，提升品牌形象和在线业务转化率。'
        : 'Responsive website design, SEO optimization, enhancing brand image and online business conversion rates.',
      IconComponent: WebDevIcon,
      features: isZh
        ? ['响应式设计', 'SEO 优化', '性能优化', '内容管理系统']
        : ['Responsive Design', 'SEO Optimization', 'Performance Optimization', 'Content Management System']
    }
  ];

  // 特色亮点
  const features = [
    {
      icon: '🚀',
      title: isZh ? '快速交付' : 'Fast Delivery',
      description: isZh ? '平均 4-8 周完成项目，敏捷开发流程确保高效交付' : 'Average 4-8 weeks project completion, agile development process ensures efficient delivery'
    },
    {
      icon: '🎯',
      title: isZh ? '精准定制' : 'Precise Customization',
      description: isZh ? '深入了解业务需求，提供量身定制的技术解决方案' : 'Deep understanding of business needs, providing tailored technical solutions'
    },
    {
      icon: '🛡️',
      title: isZh ? '安全可靠' : 'Secure & Reliable',
      description: isZh ? '企业级安全标准，99.9% 系统稳定性保障' : 'Enterprise-level security standards, 99.9% system stability guarantee'
    },
    {
      icon: '💡',
      title: isZh ? '技术创新' : 'Technical Innovation',
      description: isZh ? '采用最新技术栈，持续优化产品性能和用户体验' : 'Using the latest technology stack, continuously optimizing product performance and user experience'
    }
  ];

  // 客户案例
  const caseStudies = isZh ? [
    {
      title: '智能零售小程序',
      description: '为连锁零售企业开发的智能小程序，集成会员管理、在线购物、库存管理等功能。',
      result: '用户活跃度提升 300%，销售额增长 150%',
      industry: '零售电商'
    },
    {
      title: '企业管理系统',
      description: '为制造业企业定制的 ERP 系统，包含生产管理、财务管理、人力资源等模块。',
      result: '运营效率提升 40%，成本降低 25%',
      industry: '制造业'
    },
    {
      title: '在线教育平台',
      description: '构建完整的在线教育生态系统，支持直播教学、作业管理、学习分析等功能。',
      result: '学员满意度 98%，平台日活用户 10万+',
      industry: '教育培训'
    }
  ] : [
    {
      title: 'Smart Retail Mini-Program',
      description: 'Intelligent mini-program developed for retail chain enterprises, integrating member management, online shopping, inventory management and other functions.',
      result: 'User activity increased by 300%, sales growth of 150%',
      industry: 'Retail E-commerce'
    },
    {
      title: 'Enterprise Management System',
      description: 'Customized ERP system for manufacturing enterprises, including production management, financial management, human resources and other modules.',
      result: 'Operational efficiency improved by 40%, cost reduced by 25%',
      industry: 'Manufacturing'
    },
    {
      title: 'Online Education Platform',
      description: 'Built a complete online education ecosystem, supporting live teaching, homework management, learning analytics and other functions.',
      result: '98% student satisfaction, 100,000+ daily active users',
      industry: 'Education & Training'
    }
  ];

  // 客户评价
  const testimonials = isZh ? [
    {
      name: "王先生",
      role: "科技公司 CEO",
      content: "三娃软件团队专业高效，交付的小程序超出了我们的预期。客户反馈非常积极，用户活跃度显著提升！"
    },
    {
      name: "李女士",
      role: "电商平台负责人",
      content: "与三娃软件的合作非常愉快，他们提供的后端解决方案稳定可靠，支持了我们业务的快速发展。技术团队响应迅速，服务贴心。"
    },
    {
      name: "张总",
      role: "制造企业 CTO",
      content: "三娃软件为我们开发的 ERP 系统大大提升了运营效率。项目管理规范，交付质量很高，是值得信赖的技术合作伙伴。"
    }
  ] : [
    {
      name: "John Smith",
      role: "Tech Company CEO",
      content: "Sanva's team is professional and efficient. The mini-program they delivered exceeded our expectations. Customer feedback is very positive, and user activity has significantly improved!"
    },
    {
      name: "Sarah Johnson",
      role: "E-commerce Platform Manager",
      content: "Working with Sanva has been a pleasure. Their backend solutions are stable and reliable, supporting our rapid business growth. The technical team responds quickly and provides thoughtful service."
    },
    {
      name: "Michael Chen",
      role: "Manufacturing Company CTO",
      content: "The ERP system developed by Sanva has greatly improved our operational efficiency. Project management is standardized, delivery quality is high, and they are a trustworthy technical partner."
    }
  ];

  return (
    <>
      {/* Hero 区域 - Apple 风格简洁设计 */}
      <section className="relative py-16 md:py-24 bg-white text-neutral-900">
        {/* Apple 风格：极简白色背景，去除所有装饰元素 */}

        <Container>
          <div className="md:flex md:items-center md:justify-between">
            <div className="md:w-1/2">
              <motion.h1
                // 修改 - H1 调整到更贴近 Apple HIG 的层级（桌面约 48px）
                // Confirmed via mcp-feedback-enhanced
                className="text-4xl md:text-5xl font-bold tracking-tight mb-6 leading-tight"
                initial={{ opacity: 0, y: 16 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {t('home.hero.title', localeTyped)}
              </motion.h1>
              <motion.p
                className="text-lg md:text-xl text-neutral-600 mb-8 max-w-2xl"
                initial={{ opacity: 0, y: 16 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.05 }}
              >
                {t('home.hero.subtitle', localeTyped)}
              </motion.p>
              <motion.div
                className="flex flex-col sm:flex-row gap-4"
                initial={{ opacity: 0, y: 16 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <Link href={`/${locale}/contact`} prefetch={false}>
                  <Button size="lg" variant="primary" icon={NavigationIcons.Arrow} iconPosition="right">
                    {t('home.hero.cta', localeTyped)}
                  </Button>
                </Link>
                <Link href={`/${locale}/services`} prefetch={false}>
                  <Button size="lg" variant="outline" className="border-neutral-300 text-neutral-800 hover:bg-neutral-100 hover:text-neutral-900">
                    {localeTyped === 'zh' ? '了解服务' : 'Learn More'}
                  </Button>
                </Link>
              </motion.div>
            </div>
            {/* 替换右侧视觉：使用更克制的“设备卡片”占位，无 emoji、无夸张渐变 [Apple HIG] */}
            <div className="hidden md:block md:w-1/2 mt-12 md:mt-0">
              <motion.div
                className="relative h-80 flex items-center justify-center"
                initial={{ opacity: 0, scale: 0.98 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.1 }}
              >
                <div className="w-[18rem] h-[12rem] rounded-3xl surface-translucent ring-1 ring-neutral-200/70 shadow-soft" />
              </motion.div>
            </div>
          </div>
        </Container>
      </section>

      {/* 服务介绍区域 - Apple 风格简洁背景 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-14"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
              {t('home.services.title', localeTyped)}
            </h2>
            <p className="text-base md:text-lg text-neutral-600 max-w-3xl mx-auto">
              {t('home.services.subtitle', localeTyped)}
            </p>

            {/* 关键指标区域：紧随 Hero，强调可信度 [Apple HIG - 模块重排，Confirmed via mcp-feedback-enhanced] */}
            <section className="py-8 md:py-10 bg-neutral-50">
              <Container>
                <Stats
                  items={[
                    { label: localeTyped === 'zh' ? '交付项目' : 'Projects', value: '80+' },
                    { label: localeTyped === 'zh' ? '客户满意度' : 'CSAT', value: '98%' },
                    { label: localeTyped === 'zh' ? '覆盖行业' : 'Industries', value: '12+' },
                    { label: localeTyped === 'zh' ? '平均周期' : 'Avg Cycle', value: '4-8w' },
                  ]}
                  className="max-w-5xl mx-auto"
                />
              </Container>
            </section>
          </motion.div>
      {/* 品牌墙已调整为独立区块并弱化视觉权重 [Apple HIG - 模块重排，Confirmed via mcp-feedback-enhanced] */}

          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-8">
            {services.map((service, index) => (
              <Card key={`${index}-${locale}`} hover border rounded="xl" shadow="sm" className="bg-white">
                <CardContent className="text-center p-6">
                  <div className="mb-5 flex justify-center">
                    <div className="p-3 bg-neutral-50 rounded-xl ring-1 ring-neutral-200/70">
                      <service.IconComponent className="w-7 h-7 text-primary-600" />
                    </div>
                  </div>
                  <CardTitle size="md" className="mb-2 text-neutral-900">
                    {service.title}
                  </CardTitle>
                  <CardDescription className="text-sm leading-relaxed">
                    {service.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* 特色亮点部分 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {isZh ? '为什么选择我们' : 'Why Choose Us'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {isZh
                ? '我们专注于提供高质量的软件开发服务，用专业的技术和贴心的服务帮助客户实现业务目标'
                : 'We focus on providing high-quality software development services, helping clients achieve business goals with professional technology and thoughtful service'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {features.map((feature, index) => (
              <FeatureCard key={index} {...feature} />
            ))}
          </motion.div>
        </div>
      </section>

      {/* 客户案例部分 */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {isZh ? '成功案例' : 'Success Stories'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {isZh
                ? '我们为不同行业的客户提供了优质的解决方案，帮助他们实现业务目标'
                : 'We have provided quality solutions for clients in different industries, helping them achieve their business goals'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-3 gap-8"
          >
            {caseStudies.map((caseStudy, index) => (
              <CaseStudyCard key={index} {...caseStudy} />
            ))}
          </motion.div>
        </div>
      </section>

      {/* 客户评价区域 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {t('home.testimonials.title', localeTyped)}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {t('home.testimonials.subtitle', localeTyped)}
            </p>
          </motion.div>
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto"
          >
            {testimonials.map((testimonial, index) => (
              <Testimonial
                key={`${index}-${locale}`}
                quote={testimonial.content}
                author={testimonial.name}
                role={testimonial.role}
              />
            ))}
          </motion.div>
        </div>
      </section>

      {/* 快速 CTA：Apple 风格简洁设计，按钮主次分明 [Apple HIG] */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card border rounded="xl" shadow="sm" className="text-center p-10 bg-neutral-50">
            <CardContent>
              <h3 className="text-2xl md:text-3xl font-semibold text-neutral-900 mb-4">
                {t('home.quickNav.title', localeTyped)}
              </h3>
              <div className="mt-6 flex flex-col sm:flex-row justify-center gap-3">
                <Link href={`/${locale}/about`} prefetch={false}>
                  <Button variant="outline" size="lg" className="border-neutral-300 text-neutral-800 hover:bg-neutral-100 hover:text-neutral-900">
                    {localeTyped === 'zh' ? '关于我们' : 'About Us'}
                  </Button>
                </Link>
                <Link href={`/${locale}/services`} prefetch={false}>
                  <Button variant="outline" size="lg" className="border-neutral-300 text-neutral-800 hover:bg-neutral-100 hover:text-neutral-900">
                    {localeTyped === 'zh' ? '服务详情' : 'Our Services'}
                  </Button>
                </Link>
                <Link href={`/${locale}/contact`} prefetch={false}>
                  <Button variant="primary" size="lg" icon={ContactIcons.Email} iconPosition="left">
                    {localeTyped === 'zh' ? '联系我们' : 'Contact Us'}
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </>
  );
}

{"common": {"contactUs": "联系我们", "services": "服务", "about": "关于", "home": "首页", "learnMore": "了解更多", "loading": "加载中...", "submit": "提交", "submitting": "提交中...", "language": "语言", "openMainMenu": "打开主菜单", "closeMainMenu": "关闭主菜单"}, "navigation": {"home": "首页", "services": "服务", "about": "关于我们", "portfolio": "作品集", "blog": "博客", "team": "团队", "caseStudies": "客户案例", "pricing": "价格方案", "contact": "联系我们"}, "home": {"hero": {"title": "打造高效、优雅的数字解决方案", "subtitle": "三娃软件开发工作室专注于为企业和个人提供专业的应用开发、小程序开发和后端开发服务。", "cta": "联系我们"}, "services": {"title": "我们的服务", "subtitle": "提供全面的软件开发解决方案，满足您的各种数字需求", "appDev": {"title": "应用开发", "description": "iOS/Android 原生或跨平台应用开发，包括 React Native、Flutter 等技术栈"}, "miniProgram": {"title": "小程序开发", "description": "微信小程序、支付宝小程序、抖音小程序等，强调快速部署和优质用户体验"}, "backend": {"title": "后端开发", "description": "RESTful API、数据库设计、云服务（如 AWS、阿里云）等解决方案"}, "webDev": {"title": "网站开发", "description": "响应式网站、企业官网、电商平台等，支持多终端访问"}, "globalPlatforms": {"title": "海外平台开发", "description": "Google Play应用、App Store应用、Facebook小游戏、WhatsApp Business等海外平台开发"}}, "testimonials": {"title": "客户评价", "subtitle": "我们的客户对我们的服务是如何评价的"}, "quickNav": {"title": "了解更多关于我们的信息"}}, "services": {"title": "我们的服务", "subtitle": "我们提供专业的软件开发服务，包括应用开发、小程序开发和后端开发", "sectionTitle": "全方位软件开发解决方案", "sectionSubtitle": "无论您是需要移动应用、小程序还是后端系统，我们都能为您提供优质的开发服务，帮助您的业务实现数字化转型。", "appDevelopment": {"title": "应用开发", "description": "我们提供iOS和Android平台的原生应用开发，以及使用跨平台技术的应用开发服务。", "features": {"native": "原生应用开发 (iOS/Android)", "crossPlatform": "跨平台应用开发", "uiUx": "用户界面和体验设计", "maintenance": "应用维护和更新", "storeSupport": "应用商店上架支持"}}, "miniProgram": {"title": "小程序开发", "description": "我们专注于微信小程序、支付宝小程序、抖音小程序等各类小程序的开发，帮助企业快速触达用户。", "features": {"wechat": "微信小程序开发", "alipay": "支付宝小程序开发", "douyin": "抖音小程序开发", "multiPlatform": "多平台小程序解决方案", "ecommerce": "小程序商城和支付集成"}}, "backend": {"title": "后端开发", "description": "我们提供可靠、安全、高性能的后端开发服务，为您的应用提供强大的数据支持。", "features": {"api": "RESTful API 开发", "database": "数据库设计和优化", "auth": "用户认证与授权", "cloud": "云服务集成 (AWS、阿里云等)", "server": "服务器配置和维护"}}, "globalPlatforms": {"title": "海外平台开发", "description": "我们为企业提供海外平台的开发服务，帮助您的产品触达全球用户。", "features": {"googlePlay": "Google Play应用开发", "appStore": "App Store应用开发", "facebook": "Facebook小游戏开发", "whatsapp": "WhatsApp Business集成", "telegram": "Telegram Bot开发", "instagram": "Instagram API集成", "twitter": "Twitter/X API集成", "linkedin": "LinkedIn应用开发"}}, "process": {"title": "我们的开发流程", "subtitle": "透明高效的开发流程，确保项目顺利进行和按时交付", "steps": {"analysis": {"title": "需求分析", "description": "深入了解您的业务需求，确定项目目标和功能范围"}, "design": {"title": "设计规划", "description": "制定技术方案和设计原型，确保最佳用户体验"}, "development": {"title": "开发实施", "description": "按照设计方案进行开发，定期汇报进度和成果"}, "delivery": {"title": "测试交付", "description": "全面测试应用功能，确保质量后进行部署和交付"}}}}, "contact": {"title": "联系我们", "subtitle": "我们期待听取您的需求，随时为您提供专业支持", "methods": {"title": "联系方式", "email": "电子邮件", "workTime": "工作时间", "workHours": "周一至周五 9:00 - 18:00"}, "followUs": "关注我们", "form": {"title": "发送消息", "subtitle": "我们期待您的来信，专业团队将为您提供量身定制的解决方案", "name": "姓名", "email": "电子邮件", "phone": "电话（选填）", "message": "需求描述", "required": "*", "send": "💌 立即发送", "success": {"title": "🎉 感谢您的留言！", "message": "我们已收到您的消息，专业团队将在24小时内与您联系。", "urgent": "如有紧急需求，请直接发送邮件至 <EMAIL>"}, "errors": {"nameRequired": "请输入您的姓名", "emailRequired": "请输入您的邮箱", "emailInvalid": "请输入有效的邮箱地址", "messageRequired": "请输入您的需求描述"}}, "faq": {"title": "常见问题", "subtitle": "这里是一些客户常问的问题和答案，如果您有其他问题，请随时联系我们", "questions": {"timeline": {"q": "项目开发周期一般多长？", "a": "项目开发周期取决于项目规模和复杂度。简单的小程序可能需要2-4周，复杂的应用可能需要2-3个月。我们会在项目开始前提供详细的时间估计。"}, "pricing": {"q": "你们的收费标准是什么？", "a": "我们根据项目复杂度、功能数量和开发时间来确定价格。我们提供免费的项目评估和报价服务，请联系我们获取详细信息。"}, "maintenance": {"q": "开发完成后提供维护服务吗？", "a": "是的，我们提供项目交付后的技术支持和维护服务。我们通常会提供1-3个月的免费维护期，之后可以签订长期维护合同。"}, "modification": {"q": "可以修改现有的应用或小程序吗？", "a": "可以的。我们可以接手并修改现有项目，优化功能或修复问题。我们会先对代码进行评估，然后提供修改方案和报价。"}}}}, "about": {"title": "关于我们", "subtitle": "了解三娃软件开发工作室的背景和专业能力", "introduction": {"title": "工作室简介", "paragraphs": {"first": "三娃软件开发工作室成立于2023年，是一家专注于为企业和个人提供高质量软件解决方案的团队。我们的服务范围涵盖移动应用开发、小程序开发和后端系统开发。", "second": "我们的团队由经验丰富的开发工程师和设计师组成，每一位成员都拥有扎实的技术功底和创新思维。我们注重技术的前沿发展，不断学习和应用新技术，以确保为客户提供最优质的服务。", "third": "在三娃软件，我们相信技术应该为人服务，为企业创造价值。我们致力于用技术解决实际问题，帮助客户实现数字化转型，提升运营效率，增强市场竞争力。"}}, "values": {"title": "我们的核心价值观", "subtitle": "这些价值观指导我们的日常工作，帮助我们为客户提供最优质的服务", "items": {"professional": {"title": "专业", "description": "我们拥有丰富的技术经验和行业知识，为客户提供专业的软件解决方案。"}, "efficient": {"title": "高效", "description": "我们注重开发效率和项目进度管理，确保按时交付高质量的产品。"}, "customerFirst": {"title": "客户至上", "description": "我们以客户需求为中心，提供个性化解决方案和贴心的售后服务。"}}}, "team": {"title": "我们的团队", "subtitle": "由经验丰富的专业人士组成，致力于为您提供最优质的服务", "members": {"founder": {"name": "张", "role": "创始人 / 首席开发工程师", "description": "拥有10年软件开发经验，专注于移动应用和小程序开发。"}, "designer": {"name": "李", "role": "UI/UX设计师", "description": "擅长创造美观且易用的用户界面，关注用户体验的每一个细节。"}, "backend": {"name": "王", "role": "后端开发工程师", "description": "精通云服务和数据库设计，构建稳定高效的后端系统。"}}}}, "team": {"title": "我们的团队", "subtitle": "专业的开发团队，为您提供优质的软件开发服务", "stats": {"teamMembers": "团队成员", "projectExperience": "项目经验", "clientSatisfaction": "客户满意度", "certifications": "技术认证", "yearsPlus": "5年+", "designExperience": "5年+ 设计经验"}, "members": {"founder": {"name": "张三", "role": "创始人 / 首席开发工程师", "bio": "拥有10年软件开发经验，专注于移动应用和小程序开发，精通React Native、Flutter等跨平台技术。", "experience": "10年+ 开发经验", "education": "计算机科学硕士", "achievements": ["主导开发了50+个成功项目", "获得多项技术认证", "在技术社区发表多篇文章"]}, "designer": {"name": "陈美丽", "role": "UI/UX 设计师", "bio": "专注于用户体验设计和视觉设计，擅长将复杂的业务需求转化为简洁优雅的用户界面。", "experience": "5年+ 设计经验", "education": "视觉传达设计学士", "achievements": ["设计的产品获得红点设计奖", "用户体验优化提升转化率30%+", "建立了完整的设计系统"]}}}, "portfolio": {"viewDetails": "查看详情", "coreFeatures": "核心功能："}, "footer": {"tagline": "打造高效、优雅的数字解决方案", "navigation": "导航", "services": "服务", "contact": "联系方式", "appDevelopment": "应用开发", "miniProgramDevelopment": "小程序开发", "backendDevelopment": "后端开发", "globalPlatformDevelopment": "海外平台开发", "allRightsReserved": "版权所有"}}
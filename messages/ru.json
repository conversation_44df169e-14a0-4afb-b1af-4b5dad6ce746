{"common": {"contactUs": "Связаться с нами", "services": "Услуги", "about": "О нас", "home": "Главная", "learnMore": "Подробнее", "loading": "Загрузка...", "submit": "Отправить", "submitting": "Отправка..."}, "navigation": {"home": "Главная", "services": "Услуги", "about": "О нас", "portfolio": "Портфолио", "blog": "Блог", "team": "Команда", "caseStudies": "Кейсы", "pricing": "Цены", "contact": "Контакты"}, "home": {"hero": {"title": "Создание эффективных и элегантных цифровых решений", "subtitle": "Sanva Software Development Studio специализируется на предоставлении профессиональных услуг разработки приложений, мини-программ и бэкенд-разработки для бизнеса и частных лиц.", "cta": "Связаться с нами"}, "services": {"title": "Наши услуги", "subtitle": "Комплексные решения для разработки программного обеспечения для удовлетворения всех ваших цифровых потребностей", "appDev": {"title": "Разработка приложений", "description": "Нативная разработка приложений для iOS/Android или кроссплатформенная разработка с использованием React Native, Flutter и других технологий"}, "miniProgram": {"title": "Разработка мини-программ", "description": "Мини-программы WeChat, Alipay, TikTok и другие с быстрым развертыванием и отличным пользовательским опытом"}, "backend": {"title": "Бэкенд-разработка", "description": "RESTful API, проектирование баз данных, облачные сервисы (AWS, Alibaba Cloud и др.)"}, "webDev": {"title": "Веб-разработка", "description": "Адаптивные сайты, корпоративные веб-сайты, платформы электронной коммерции с доступом с различных устройств"}, "globalPlatforms": {"title": "Разработка для глобальных платформ", "description": "Приложения для Google Play, App Store, мини-игры Facebook, WhatsApp Business и другие глобальные платформы"}}, "testimonials": {"title": "Отзывы клиентов", "subtitle": "Что наши клиенты говорят о наших услугах"}, "quickNav": {"title": "Узнайте больше о нас"}}, "services": {"title": "Наши услуги", "subtitle": "Мы предоставляем профессиональные услуги разработки программного обеспечения, включая разработку приложений, мини-программ и бэкенда", "sectionTitle": "Комплексные решения для разработки программного обеспечения", "sectionSubtitle": "Независимо от того, нужны ли вам мобильные приложения, мини-программы или бэкенд-системы, мы предоставляем качественные услуги разработки, чтобы помочь вашему бизнесу достичь цифровой трансформации.", "appDevelopment": {"title": "Разработка приложений", "description": "Мы предоставляем услуги разработки нативных приложений для iOS и Android, а также кроссплатформенную разработку с использованием современных технологий.", "features": {"native": "Разработка нативных приложений (iOS/Android)", "crossPlatform": "Кроссплатформенная разработка приложений", "uiUx": "UI/UX дизайн", "maintenance": "Поддержка и обновления приложений", "storeSupport": "Поддержка публикации в магазинах приложений"}}, "miniProgram": {"title": "Разработка мини-программ", "description": "Мы специализируемся на разработке различных мини-программ, включая WeChat, Alipay и TikTok, чтобы помочь бизнесу быстро достичь пользователей.", "features": {"wechat": "Разработка мини-программ WeChat", "alipay": "Разработка мини-програ<PERSON><PERSON>", "douyin": "Разработка мини-программ TikTok", "multiPlatform": "Мультиплатформенные решения мини-программ", "ecommerce": "Интеграция электронной коммерции и платежей в мини-программы"}}, "backend": {"title": "Бэкенд-разработка", "description": "Мы предоставляем надежные, безопасные и высокопроизводительные услуги бэкенд-разработки для поддержки ваших приложений.", "features": {"api": "Разработка RESTful API", "database": "Проектирование и оптимизация баз данных", "auth": "Аутентификация и авторизация пользователей", "cloud": "Интеграция облачных сервисов (AWS, Alibaba Cloud и др.)", "server": "Настройка и обслуживание серверов"}}, "globalPlatforms": {"title": "Разработка для глобальных платформ", "description": "Мы предоставляем услуги разработки для глобальных платформ, чтобы помочь вашим продуктам достичь пользователей по всему миру.", "features": {"googlePlay": "Разработка приложений для Google Play", "appStore": "Разработка приложений для App Store", "facebook": "Разработка мини-игр Facebook", "whatsapp": "Интеграция WhatsApp Business", "telegram": "Разработка ботов Telegram", "instagram": "Интеграция API Instagram", "twitter": "Интеграция API Twitter/X", "linkedin": "Разработка приложений LinkedIn"}}, "process": {"title": "Наш процесс разработки", "subtitle": "Прозрачный и эффективный процесс разработки, обеспечивающий плавный ход проекта и своевременную доставку", "steps": {"analysis": {"title": "Ана<PERSON>из требований", "description": "Глубокое понимание потребностей вашего бизнеса, определение целей проекта и области функциональности"}, "design": {"title": "Диза<PERSON>н и планирование", "description": "Создание технических решений и прототипов дизайна для обеспечения оптимального пользовательского опыта"}, "development": {"title": "Реализация разработки", "description": "Разработка в соответствии с планами дизайна с регулярными отчетами о прогрессе и результатах"}, "delivery": {"title": "Тестирование и доставка", "description": "Комплексное тестирование функций приложения, обеспечение качества перед развертыванием и доставкой"}}}}, "contact": {"title": "Связаться с нами", "subtitle": "Мы ждем ваших потребностей и готовы предоставить профессиональную поддержку в любое время", "methods": {"title": "Контактная информация", "email": "Электронная почта", "workTime": "Рабочие часы", "workHours": "Понедельник - пятница 9:00 - 18:00"}, "followUs": "Следите за нами", "form": {"title": "Отправить сообщение", "subtitle": "Мы ждем вашего сообщения. Наша профессиональная команда предоставит вам индивидуальные решения", "name": "Имя", "email": "Электронная почта", "phone": "Телефон (необязательно)", "message": "Описание требований", "required": "*", "send": "💌 Отправить сейчас", "success": {"title": "🎉 Спасибо за ваше сообщение!", "message": "Мы получили ваше сообщение. Наша профессиональная команда свяжется с вами в течение 24 часов.", "urgent": "Для срочных потребностей отправьте электронное письмо напрямую на <EMAIL>"}, "errors": {"nameRequired": "Пожалуйста, введите ваше имя", "emailRequired": "Пожалуйста, введите ваш email", "emailInvalid": "Пожалуйста, введите действительный email адрес", "messageRequired": "Пожалуйста, введите описание ваших требований"}}, "faq": {"title": "Часто задаваемые вопросы", "subtitle": "Вот некоторые часто задаваемые вопросы от наших клиентов. Если у вас есть другие вопросы, пожалуйста, свяжитесь с нами", "questions": {"timeline": {"q": "Сколько обычно занимает разработка проекта?", "a": "Временные рамки разработки проекта зависят от масштаба и сложности проекта. Простые мини-программы могут занять 2-4 недели, в то время как сложные приложения могут потребовать 2-3 месяца. Мы предоставляем детальные временные оценки перед началом проекта."}, "pricing": {"q": "Каковы ваши стандарты ценообразования?", "a": "Мы определяем цены на основе сложности проекта, количества функций и времени разработки. Мы предоставляем бесплатные услуги оценки и составления сметы проектов. Пожалуйста, свяжитесь с нами для получения подробной информации."}, "maintenance": {"q": "Предоставляете ли вы услуги обслуживания после разработки?", "a": "Да, мы предоставляем техническую поддержку и услуги обслуживания после доставки проекта. Обычно мы предоставляем 1-3 месяца бесплатного периода обслуживания, за которым следуют долгосрочные контракты на обслуживание."}, "modification": {"q": "Можете ли вы модифицировать существующие приложения или мини-программы?", "a": "Да. Мы можем взять на себя и модифицировать существующие проекты, оптимизировать функции или исправить проблемы. Сначала мы оцениваем код, затем предоставляем планы модификации и котировки."}}}}, "about": {"title": "О нас", "subtitle": "Узнайте больше об истории и профессиональных возможностях Sanva Software Development Studio", "introduction": {"title": "Представление Студии", "paragraphs": {"first": "Sanva Software Development Studio была основана в 2023 году, специализируясь на предоставлении высококачественных программных решений для предприятий и частных лиц. Наши услуги охватывают разработку мобильных приложений, разработку мини-программ и разработку backend-систем.", "second": "Наша команда состоит из опытных инженеров-разработчиков и дизайнеров, каждый из которых обладает твердыми техническими навыками и инновационным мышлением. Мы сосредоточены на развитии передовых технологий, постоянно изучая и применяя новые технологии, чтобы обеспечить предоставление услуг высочайшего качества нашим клиентам.", "third": "В Sanva Software мы верим, что технологии должны служить людям и создавать ценность для бизнеса. Мы стремимся решать реальные проблемы с помощью технологий, помогая клиентам достичь цифровой трансформации, повысить операционную эффективность и укрепить рыночную конкурентоспособность."}}, "values": {"title": "Наши Основные Ценности", "subtitle": "Эти ценности направляют нашу ежедневную работу и помогают нам предоставлять услуги высочайшего качества нашим клиентам", "items": {"professional": {"title": "Профессиональность", "description": "У нас есть обширный технический опыт и знания в отрасли для предоставления профессиональных программных решений нашим клиентам."}, "efficient": {"title": "Эффективность", "description": "Мы сосредоточены на эффективности разработки и управлении прогрессом проекта для обеспечения своевременной доставки высококачественных продуктов."}, "customerFirst": {"title": "Клиент Прежде Всего", "description": "Мы сосредоточены на потребностях клиентов, предоставляя персонализированные решения и внимательное послепродажное обслуживание."}}}, "team": {"title": "Наша Команда", "subtitle": "Состоящая из опытных профессионалов, преданных предоставлению вам услуг высочайшего качества", "members": {"founder": {"name": "<PERSON><PERSON><PERSON><PERSON>", "role": "Основатель / Ведущий Разработчик", "description": "С 10-летним опытом разработки программного обеспечения, специализирующийся на разработке мобильных приложений и мини-программ."}, "designer": {"name": "Сара Ли", "role": "UI/UX Дизайнер", "description": "Умелая в создании красивых и удобных интерфейсов, сосредоточенная на каждой детали пользовательского опыта."}, "backend": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "role": "Backend Разработчик", "description": "Эксперт по облачным сервисам и проектированию баз данных, создающий стабильные и эффективные backend-системы."}}}}, "team": {"title": "Наша Команда", "subtitle": "Профессиональная команда разработки, предоставляющая качественные услуги разработки программного обеспечения", "stats": {"teamMembers": "Члены Команды", "projectExperience": "Опыт Проектов", "clientSatisfaction": "Удовлетворенность Клиентов", "certifications": "Сертификации", "yearsPlus": "5+ лет", "designExperience": "5+ лет Дизайн"}}, "portfolio": {"viewDetails": "Подробнее", "coreFeatures": "Основные функции:"}, "footer": {"tagline": "Создание Эффективных и Элегантных Цифровых Решений", "navigation": "Навигация", "services": "Услуги", "contact": "Контакты", "appDevelopment": "Разработка Приложений", "miniProgramDevelopment": "Разработка Мини-Программ", "backendDevelopment": "Backend Разработка", "globalPlatformDevelopment": "Разработка Глобальных Платформ", "allRightsReserved": "Все права защищены"}}
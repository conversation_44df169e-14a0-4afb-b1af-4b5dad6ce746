{"common": {"contactUs": "Contate-nos", "services": "Serviços", "about": "Sobre", "home": "Início", "learnMore": "<PERSON><PERSON> mais", "loading": "Carregando...", "submit": "Enviar", "submitting": "Enviando..."}, "navigation": {"home": "Início", "services": "Serviços", "about": "Sobre nós", "portfolio": "Portfólio", "blog": "Blog", "team": "Equipe", "caseStudies": "Estudos de Caso", "pricing": "Preços", "contact": "Contato"}, "home": {"hero": {"title": "Criando soluções digitais eficientes e elegantes", "subtitle": "Sanva Software Development Studio especializa-se em fornecer serviços profissionais de desenvolvimento de aplicativos, mini-programas e desenvolvimento backend para empresas e indivíduos.", "cta": "Contate-nos"}, "services": {"title": "<PERSON><PERSON><PERSON>r<PERSON>", "subtitle": "Soluções abrangentes de desenvolvimento de software para atender todas as suas necessidades digitais", "appDev": {"title": "Desenvolvimento de aplicativos", "description": "Desenvolvimento de aplicativos nativos iOS/Android ou multiplataforma usando React Native, Flutter e outras stacks tecnológicas"}, "miniProgram": {"title": "Desenvolvimento de mini-programas", "description": "Mini-programas do WeChat, Alipay, TikTok e mais com implantação rápida e excelente experiência do usuário"}, "backend": {"title": "Desenvolvimento backend", "description": "APIs RESTful, design de banco de dados, serviços em nuvem (AWS, Alibaba Cloud, etc.)"}, "webDev": {"title": "Desenvolvimento web", "description": "Sites responsivos, sites corporativos, plataformas de e-commerce com acesso multi-dispositivo"}, "globalPlatforms": {"title": "Desenvolvimento de plataformas globais", "description": "Aplicativos Google Play, App Store, mini-jogos do Facebook, WhatsApp Business e outras plataformas globais"}}, "testimonials": {"title": "Depoimentos de clientes", "subtitle": "O que nossos clientes dizem sobre nossos serviços"}, "quickNav": {"title": "Saiba mais sobre nós"}}, "services": {"title": "<PERSON><PERSON><PERSON>r<PERSON>", "subtitle": "Oferecemos serviços profissionais de desenvolvimento de software incluindo desenvolvimento de aplicativos, mini-programas e backend", "sectionTitle": "Soluções abrangentes de desenvolvimento de software", "sectionSubtitle": "Seja você precisando de aplicativos móveis, mini-programas ou sistemas backend, oferecemos serviços de desenvolvimento de qualidade para ajudar seu negócio a alcançar a transformação digital.", "appDevelopment": {"title": "Desenvolvimento de aplicativos", "description": "Oferecemos serviços de desenvolvimento de aplicativos nativos para iOS e Android, bem como desenvolvimento de aplicativos multiplataforma usando tecnologias modernas.", "features": {"native": "Desenvolvimento de aplicativos nativos (iOS/Android)", "crossPlatform": "Desenvolvimento de aplicativos multiplataforma", "uiUx": "Design UI/UX", "maintenance": "Manutenção e atualizações de aplicativos", "storeSupport": "Suporte para submissão em lojas de aplicativos"}}, "miniProgram": {"title": "Desenvolvimento de mini-programas", "description": "Focamos no desenvolvimento de vários mini-programas incluindo WeChat, Alipay e TikTok para ajudar empresas a alcançar usuários rapidamente.", "features": {"wechat": "Desenvolvimento de mini-programas do WeChat", "alipay": "Desenvolvimento de mini-programas do Alipay", "douyin": "Desenvolvimento de mini-programas do TikTok", "multiPlatform": "Soluções de mini-programas multiplataforma", "ecommerce": "Integração de e-commerce e pagamentos em mini-programas"}}, "backend": {"title": "Desenvolvimento backend", "description": "Oferecemos serviços de desenvolvimento backend confiáveis, seguros e de alto desempenho para potencializar seus aplicativos.", "features": {"api": "Desenvolvimento de APIs RESTful", "database": "Design e otimização de banco de dados", "auth": "Autenticação e autorização de usuários", "cloud": "Integração de serviços em nuvem (AWS, Alibaba Cloud, etc.)", "server": "Configuração e manutenção de servidores"}}, "globalPlatforms": {"title": "Desenvolvimento de plataformas globais", "description": "Oferecemos serviços de desenvolvimento para plataformas globais para ajudar seus produtos a alcançar usuários em todo o mundo.", "features": {"googlePlay": "Desenvolvimento de aplicativos para Google Play", "appStore": "Desenvolvimento de aplicativos para App Store", "facebook": "Desenvolvimento de mini-jogos do Facebook", "whatsapp": "Integração do WhatsApp Business", "telegram": "Desenvolvimento de bots do Telegram", "instagram": "Integração da API do Instagram", "twitter": "Integração da API do Twitter/X", "linkedin": "Desenvolvimento de aplicativos do LinkedIn"}}, "process": {"title": "Nosso processo de desenvolvimento", "subtitle": "Processo de desenvolvimento transparente e eficiente garantindo progresso suave do projeto e entrega pontual", "steps": {"analysis": {"title": "Análise de requisitos", "description": "Compreensão profunda das necessidades do seu negócio, definindo objetivos do projeto e escopo de funcionalidades"}, "design": {"title": "Design e planejamento", "description": "Criação de soluções técnicas e protótipos de design para garantir experiência de usuário otimizada"}, "development": {"title": "Implementação do desenvolvimento", "description": "Desenvolvimento de acordo com planos de design com relatórios regulares de progresso e entregáveis"}, "delivery": {"title": "Testes e entrega", "description": "Testes abrangentes das funcionalidades do aplicativo, garantindo qualidade antes da implantação e entrega"}}}}, "contact": {"title": "Contate-nos", "subtitle": "Esperamos ouvir suas necessidades e fornecer suporte profissional a qualquer momento", "methods": {"title": "Informações de contato", "email": "E-mail", "workTime": "<PERSON><PERSON><PERSON><PERSON>l", "workHours": "Segunda a sexta 9:00 - 18:00"}, "followUs": "Siga-nos", "form": {"title": "Enviar mensagem", "subtitle": "Esperamos sua mensagem. Nossa equipe profissional fornecerá soluções personalizadas para você", "name": "Nome", "email": "E-mail", "phone": "Telefone (Opcional)", "message": "Descrição dos requisitos", "required": "*", "send": "💌 Enviar agora", "success": {"title": "🎉 Obrigado pela sua mensagem!", "message": "Recebemos sua mensagem. Nossa equipe profissional entrará em contato com você em 24 horas.", "urgent": "Para necessidades urgentes, envie um e-mail <NAME_EMAIL>"}, "errors": {"nameRequired": "Por favor, insira seu nome", "emailRequired": "Por favor, insira seu e-mail", "emailInvalid": "Por favor, insira um endereço de e-mail válido", "messageRequired": "Por favor, insira a descrição dos seus requisitos"}}, "faq": {"title": "<PERSON><PERSON><PERSON> frequentes", "subtitle": "Aqui estão algumas perguntas frequentes de nossos clientes. Se você tiver outras perguntas, sinta-se à vontade para entrar em contato conosco", "questions": {"timeline": {"q": "Quanto tempo normalmente leva o desenvolvimento de um projeto?", "a": "O cronograma de desenvolvimento do projeto depende da escala e complexidade do projeto. Mini-programas simples podem levar 2-4 semanas, enquanto aplicações complexas podem exigir 2-3 meses. Fornecemos estimativas de tempo detalhadas antes do início do projeto."}, "pricing": {"q": "Quais são seus padrões de preços?", "a": "Determinamos os preços com base na complexidade do projeto, número de funcionalidades e tempo de desenvolvimento. Oferecemos serviços gratuitos de avaliação e cotação de projetos. Entre em contato conosco para informações detalhadas."}, "maintenance": {"q": "Vocês fornecem serviços de manutenção após o desenvolvimento?", "a": "<PERSON><PERSON>, fornecemos suporte técnico e serviços de manutenção após a entrega do projeto. Geralmente oferecemos 1-3 meses de período de manutenção gratuito, seguido de contratos de manutenção de longo prazo."}, "modification": {"q": "Vocês podem modificar aplicativos ou mini-programas existentes?", "a": "Sim. Podemos assumir e modificar projetos existentes, otimizar funcionalidades ou corrigir problemas. Primeiro avaliamos o código, depois fornecemos planos de modificação e cotações."}}}}, "about": {"title": "Sobre nós", "subtitle": "Conheça a história e capacidades profissionais do Sanva Software Development Studio", "introduction": {"title": "Apresentação do Estúdio", "paragraphs": {"first": "O Sanva Software Development Studio foi fundado em 2023, especializando-se em fornecer soluções de software de alta qualidade para empresas e indivíduos. Nossos serviços cobrem desenvolvimento de aplicativos móveis, desenvolvimento de mini-programas e desenvolvimento de sistemas backend.", "second": "Nossa equipe é composta por engenheiros de desenvolvimento e designers experientes, cada um com habilidades técnicas sólidas e pensamento inovador. Focamos no desenvolvimento de tecnologia de ponta, aprendendo e aplicando continuamente novas tecnologias para garantir que fornecemos serviços da mais alta qualidade aos nossos clientes.", "third": "Na Sanva Software, acreditamos que a tecnologia deve servir às pessoas e criar valor para as empresas. Estamos comprometidos em resolver problemas reais com tecnologia, ajudando clientes a alcançar a transformação digital, melhorar a eficiência operacional e fortalecer a competitividade de mercado."}}, "values": {"title": "Nossos Valores Fundamentais", "subtitle": "Estes valores orientam nosso trabalho diário e nos ajudam a fornecer serviços da mais alta qualidade aos nossos clientes", "items": {"professional": {"title": "Profissional", "description": "Temos ampla experiência técnica e conhecimento da indústria para fornecer soluções de software profissionais aos nossos clientes."}, "efficient": {"title": "<PERSON><PERSON><PERSON>", "description": "Focamos na eficiência de desenvolvimento e gestão de progresso de projeto para garantir entrega pontual de produtos de alta qualidade."}, "customerFirst": {"title": "Cliente em Primeiro", "description": "Nos concentramos nas necessidades do cliente, fornecendo soluções personalizadas e atendimento pós-venda atencioso."}}}, "team": {"title": "Nossa Equipe", "subtitle": "Composta por profissionais experientes dedicados a fornecer serviços da mais alta qualidade", "members": {"founder": {"name": "<PERSON>", "role": "Fundador / Desenvolvedor Principal", "description": "Com 10 anos de experiência em desenvolvimento de software, especializado em desenvolvimento de aplicativos móveis e mini-programas."}, "designer": {"name": "<PERSON>", "role": "Designer UI/UX", "description": "Hábil em criar interfaces bonitas e amigáveis ao usuário, focando em cada detalhe da experiência do usuário."}, "backend": {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON><PERSON>", "description": "Especialista em serviços em nuvem e design de banco de dados, construindo sistemas backend estáveis e eficientes."}}}}, "team": {"title": "Nossa Equipe", "subtitle": "Equipe de desenvolvimento profissional fornecendo serviços de desenvolvimento de software de qualidade", "stats": {"teamMembers": "Me<PERSON>ros da Equipe", "projectExperience": "Experiência em Projetos", "clientSatisfaction": "Satisfação do Cliente", "certifications": "Certificações", "yearsPlus": "5+ anos", "designExperience": "5+ anos Design"}}, "portfolio": {"viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "coreFeatures": "Recursos principais:"}, "footer": {"tagline": "Criando Soluções Digitais Eficientes e Elegantes", "navigation": "Navegação", "services": "Serviços", "contact": "Contato", "appDevelopment": "Desenvolvimento de Aplicativos", "miniProgramDevelopment": "Desenvolvimento de Mini-Programas", "backendDevelopment": "Desenvolvimento Backend", "globalPlatformDevelopment": "Desenvolvimento de Plataformas Globais", "allRightsReserved": "Todos os direitos reservados"}}